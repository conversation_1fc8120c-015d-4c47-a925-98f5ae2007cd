import csv
import time
import pyotp
import random
import subprocess
import pyperclip
import os
from typing import List, Dict, Tuple, Optional

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.action_chains import ActionChains

# --- 配置信息 ---
DEFAULT_REGISTRATION_URL = "https://anyrouter.top/register?aff=sUlE"
CSV_FILE = "reg.csv"
API_KEY_FILE = "key.md"

# --- 元素定位器 (Locators) ---
class GitHubLocators:    # GitHub 登录页面元素

    LOGIN_BUTTON = (By.XPATH, "//button[contains(., 'GitHub') or contains(., 'github')] | //a[contains(., 'GitHub') or contains(., 'github')]")
    USERNAME_INPUT = (By.ID, "login_field")
    PASSWORD_INPUT = (By.ID, "password")
    SIGN_IN_BUTTON = (By.NAME, "commit")
    TOTP_INPUT = (By.ID, "app_totp")
    AUTHORIZE_BUTTON = (By.CSS_SELECTOR, ".js-oauth-authorize-btn")

class AppLocators:
    ADD_TOKEN_BUTTON = (By.XPATH, "//button[.//span[text()='添加令牌']]")
    UNLIMITED_QUOTA_BUTTON = (By.XPATH, "//button[.//span[text()='设为无限额度']]")
    SUBMIT_BUTTON = (By.XPATH, "//button[.//span[text()='提交']]")
    FIRST_COPY_BUTTON = (By.XPATH, "(//button[.//span[text()='复制']])[1]")

# --- 辅助函数 ---

def read_accounts_from_csv(file_path: str) -> List[Dict[str, str]]:
    """从CSV文件中读取账户信息。"""
    accounts = []
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.reader(csvfile)
            for i, row in enumerate(reader):
                if not row:
                    continue
                full_string = row[0].strip().strip('"')
                parts = full_string.split('----')
                if len(parts) >= 3:
                    accounts.append({
                        'github_username': parts[0].strip(),
                        'github_password': parts[1].strip(),
                        'totp_secret': parts[2].strip()
                    })
                else:
                    print(f"⚠️  警告: CSV 文件第 {i+1} 行格式不正确，跳过: '{full_string}'.")
    except FileNotFoundError:
        print(f"💥 错误: CSV 文件未找到: '{file_path}'.")
    except Exception as e:
        print(f"💥 读取 CSV 文件时发生错误: {e}")
    return accounts

def take_screenshot(driver: WebDriver, filename_prefix: str):
    """在发生错误时截取屏幕。"""
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.png"
    try:
        driver.save_screenshot(filename)
        print(f"  📸 已保存屏幕截图: {filename}")
    except Exception as e:
        print(f"  💥 无法保存屏幕截图: {e}")

def generate_fresh_totp_code(totp_secret: str) -> Optional[str]:
    """生成一个有效期足够长的TOTP验证码。"""
    try:
        totp = pyotp.TOTP(totp_secret)
        remaining_time = totp.interval - (time.time() % totp.interval)
        if remaining_time < 5:  # 增加缓冲时间
            print(f"  ⏳ 当前验证码仅剩 {remaining_time:.0f} 秒，等待新码...")
            time.sleep(remaining_time + 1)
        totp_code = totp.now()
        print(f"  🔑 已生成一个新鲜的 TOTP 验证码: {totp_code}")
        return totp_code
    except Exception as e:
        print(f"  💥 错误: 使用 pyotp 库生成验证码时出错: {e}")
        return None

def create_chrome_driver() -> Optional[WebDriver]:
    """创建并返回一个配置好的Chrome WebDriver实例。"""
    try:
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument('--log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        prefs = {"credentials_enable_service": False, "profile.password_manager_enabled": False}
        options.add_experimental_option("prefs", prefs)
        
        creation_flags = 0
        log_path = '/dev/null'
        if os.name == 'nt':
            creation_flags = subprocess.CREATE_NO_WINDOW
            log_path = 'nul'
        
        service = Service(log_path=log_path, creationflags=creation_flags)
        
        return webdriver.Chrome(service=service, options=options)
    except Exception as e:
        print(f"💥 创建 WebDriver 时发生严重错误: {e}")
        return None

def create_and_save_api_key(driver: WebDriver, account_username: str) -> bool:
    """在目标网站上为指定账户创建并保存API密钥。返回True表示成功，False表示失败。"""
    print(f"  ✨ 正在为账号 {account_username} 创建新的API密钥...")
    wait = WebDriverWait(driver, 10)
    
    try:
        random_delay = random.uniform(1, 2)
        print(f"  ⏳ 等待 {random_delay:.2f} 秒，确保密钥页面完全可交互...")
        time.sleep(random_delay)
        
        print("    ℹ️  开始执行“新建令牌”流程...")
        
        wait.until(EC.element_to_be_clickable(AppLocators.ADD_TOKEN_BUTTON)).click()
        print("    ✅ 已点击“添加令牌”。")

        wait.until(EC.element_to_be_clickable(AppLocators.UNLIMITED_QUOTA_BUTTON)).click()
        print("    ✅ 已点击“设为无限额度”。")

        wait.until(EC.element_to_be_clickable(AppLocators.SUBMIT_BUTTON)).click()
        print("    ✅ 已点击“提交”。")
        
        print("    ⏳ 等待新密钥生成...")
        copy_button = wait.until(EC.element_to_be_clickable(AppLocators.FIRST_COPY_BUTTON))
        print("    ✅ 新密钥行已出现。")

        print("    🖱️ 正在尝试从剪贴板获取API密钥...")
        api_key = ""
        for attempt in range(3):
            pyperclip.copy('') 
            driver.execute_script("arguments[0].click();", copy_button)
            time.sleep(0.5) 
            
            api_key = pyperclip.paste()
            
            if api_key and api_key.startswith("sk-"):
                print(f"    ✅ (第 {attempt + 1} 次尝试) 成功从剪贴板获取API密钥!")
                break
            else:
                print(f"    ⚠️ (第 {attempt + 1} 次尝试) 未能获取有效密钥，稍后重试...")
                api_key = "" # 保持为空，以便下次循环

        if api_key:
            print(f"    🔑 获取到的密钥为: {api_key[:8]}... ")
            with open(API_KEY_FILE, 'a', encoding='utf-8') as f:
                f.write(f"{account_username}----{api_key}\n")
            print(f"    💾 已将账号和密钥保存到 {API_KEY_FILE}")
            return True
        else:
            print("    ❌ 错误: 多次尝试后，仍未能从剪贴板获取有效的API密钥。")
            # 记录失败条目
            with open(API_KEY_FILE, 'a', encoding='utf-8') as f:
                f.write(f"{account_username}----\n")
            print(f"    💾 已将账号 {account_username} (无密钥) 保存到 {API_KEY_FILE}")
            take_screenshot(driver, f"clipboard_copy_failed_{account_username}")
            return False

    except Exception as e:
        print(f"    💥 创建API密钥过程中发生错误: {e}")
        # 记录失败条目
        with open(API_KEY_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{account_username}----\n")
        print(f"    💾 已将账号 {account_username} (无密钥) 保存到 {API_KEY_FILE}")
        take_screenshot(driver, f"api_key_error_{account_username}")
        return False

def automate_registration(account_data: Dict[str, str], registration_url: str) -> Tuple[bool, Optional[WebDriver]]:
    """执行单个账户的完整注册/登录流程。"""
    driver = create_chrome_driver()
    if not driver:
        return False, None

    try:
        
        print(f"🚀 --- 尝试注册账号: {account_data['github_username']} ---")
        print(f"  ➡️  导航到注册页面: {registration_url}")
        driver.get(registration_url)
        
        wait = WebDriverWait(driver, 10)
        
        # 1. 点击GitHub登录按钮
        github_login_button = wait.until(EC.element_to_be_clickable(GitHubLocators.LOGIN_BUTTON))
        print("  ✅ 找到 GitHub 登录/注册按钮，点击中...")
        original_window_handle = driver.current_window_handle
        github_login_button.click()
        
        # 2. 切换到GitHub窗口并登录
        wait.until(EC.number_of_windows_to_be(2))
        for handle in driver.window_handles:
            if handle != original_window_handle:
                driver.switch_to.window(handle)
                break
        
        print(f"  👤 输入 GitHub 凭据: {account_data['github_username']}...")
        wait.until(EC.url_contains("github.com/login"))
        wait.until(EC.presence_of_element_located(GitHubLocators.USERNAME_INPUT)).send_keys(account_data['github_username'])
        driver.find_element(*GitHubLocators.PASSWORD_INPUT).send_keys(account_data['github_password'])
        driver.find_element(*GitHubLocators.SIGN_IN_BUTTON).click()

        # 3. 处理2FA (如果需要)
        try:
            totp_input_field = WebDriverWait(driver, 5).until(EC.presence_of_element_located(GitHubLocators.TOTP_INPUT))
            print("  📱 检测到 GitHub 2FA 页面。")
            totp_code = generate_fresh_totp_code(account_data['totp_secret'])
            if not totp_code: 
                return False, driver
            totp_input_field.send_keys(totp_code)
            print("  ✅ 2FA 输入完毕。")
        except TimeoutException:
            print(f"  ⏭️  未检测到 GitHub 2FA 页面，跳过。")
        
        # 4. 动态等待授权页面或最终跳转 (优化后逻辑)
        print("  ⏳ 动态等待授权页面或最终跳转...")
        try:
            # 等待15秒，看是先等到最终URL，还是先等到授权按钮
            wait_for_redirect_or_auth = WebDriverWait(driver, 15)
            
            # `EC.any_of` 会返回第一个成功的条件的结果
            # url_contains 返回 True, visibility_of_element_located 返回 WebElement
            result = wait_for_redirect_or_auth.until(
                EC.any_of(
                    EC.url_contains("console/token"),
                    EC.visibility_of_element_located(GitHubLocators.AUTHORIZE_BUTTON)
                )
            )

            # Case 1: 直接跳转成功 (url_contains 返回 True)
            if result is True:
                print(f"  ✅ 已直接跳转到目标页面。")
                print(f"🎉 账号 {account_data['github_username']} 注册/登录成功！")
                return True, driver
            
            # Case 2: 出现授权按钮 (visibility_of_element_located 返回 WebElement)
            else:
                authorize_button = result
                print("  🛡️  检测到 GitHub 授权页面。")
                print("  ⏳ 等待3秒后自动点击...")
                time.sleep(3)
                
                print("  🖱️  模拟真人移动鼠标并点击 'Authorize' 按钮...")
                ActionChains(driver).move_to_element(authorize_button).click().perform()
                print("  ✅ 授权点击已执行。")

                # 点击后，需要再次等待最终的跳转
                print("  ⏳ 等待授权后跳转...")
                WebDriverWait(driver, 20).until(EC.url_contains("console/token"))
                print(f"🎉 账号 {account_data['github_username']} 注册/登录成功！")
                return True, driver

        except TimeoutException:
            # 如果15秒内，最终URL和授权按钮都没出现
            print(f"❌ 账号 {account_data['github_username']} 失败: 既未跳转到目标URL，也未等到授权页面。")
            take_screenshot(driver, "dynamic_wait_timeout")
            return False, driver

    except Exception as e:
        print(f"💥 账号 {account_data['github_username']} 自动化过程中发生未预期错误: {e}")
        take_screenshot(driver, "unexpected_error")
        return False, driver

def main():
    """脚本主执行函数"""
    print("🚀 --- 自动化注册脚本启动 --- 🚀")
    print("-" * 50)
    custom_url = input(f"请输入注册URL (直接回车将使用默认地址: {DEFAULT_REGISTRATION_URL}):\n> ")
    final_url = custom_url.strip() or DEFAULT_REGISTRATION_URL
    print(f"➡️  将使用地址: {final_url}")
    print("-" * 50)
    
    choice = input("是否在注册成功后，自动新建并保存API密钥? (默认Y，输入N取消):\n> ").strip().upper()
    create_api_key_enabled = (choice != 'N')
    
    if create_api_key_enabled:
        print("✅ 已启用: 注册成功后将自动创建API密钥。")
        print("   (请确保已安装 'pyperclip' 库: pip install pyperclip)")
    else:
        print("❌ 已禁用: 注册成功后不会创建API密钥。")
    print("-" * 50)
    
    accounts = read_accounts_from_csv(CSV_FILE)
    if not accounts:
        print("🤷 没有可注册的账号。请检查 reg.csv 文件是否正确或为空。")
        return

    total_accounts = len(accounts)
    print(f"📂 找到 {total_accounts} 个账号待注册。")
    success_count = 0
    failure_count = 0
    
    for i, account in enumerate(accounts):
        print("\n" + "="*50)
        print(f"▶️  进度: ({i + 1}/{total_accounts})")
        
        driver_instance = None
        try:
            login_success, driver_instance = automate_registration(account, final_url)
            
            if login_success and driver_instance:
                if create_api_key_enabled:
                    key_success = create_and_save_api_key(driver_instance, account['github_username'])
                    if key_success:
                        success_count += 1
                    else:
                        # 密钥创建失败，计为整体失败
                        failure_count += 1
                else:
                    # 登录成功且无需创建密钥，计为成功
                    success_count += 1
            else:
                # 登录失败
                failure_count += 1
        finally:
            if driver_instance:
                driver_instance.quit()

        completed_count = success_count + failure_count
        pending_count = total_accounts - completed_count
        progress_bar = "✅" * success_count + "❌" * failure_count + "⏳" * pending_count
        print(f"📊 当前状态: {progress_bar} [{completed_count}/{total_accounts}]")

        if i < len(accounts) - 1:
            random_delay = random.uniform(1, 5)
            print(f"⏳ --- 等待 {random_delay:.2f} 秒后处理下一个账号 ---")
            time.sleep(random_delay)

    print("\n" + "="*50)
    print("🏁 --- 所有账号处理完成 --- 🏁")
    print(f"✔️  成功: {success_count}")
    print(f"❌ 失败: {failure_count}")

# --- 主程序入口 ---
if __name__ == "__main__":
    main()