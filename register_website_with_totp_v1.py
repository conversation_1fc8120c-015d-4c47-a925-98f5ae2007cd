import csv
import time
import pyotp
import random
import subprocess # --- 关键: 导入 subprocess 模块 ---
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service 

# --- 配置信息 ---
DEFAULT_REGISTRATION_URL = "https://anyrouter.top/register?aff=sUlE"
CSV_FILE = "reg.csv"

# --- 辅助函数 ---

def read_accounts_from_csv(file_path):
    accounts = []
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as csvfile: 
            reader = csv.reader(csvfile)
            for i, row in enumerate(reader):
                if row:
                    full_string = row[0].strip().strip('"')
                    parts = full_string.split('----')
                    if len(parts) >= 3:
                        accounts.append({
                            'github_username': parts[0].strip(),
                            'github_password': parts[1].strip(),
                            'totp_secret': parts[2].strip()
                        })
                    else:
                        print(f"⚠️  警告: CSV 文件第 {i+1} 行格式不正确，跳过: '{full_string}'.")
    except FileNotFoundError:
        print(f"💥 错误: CSV 文件未找到: '{file_path}'.")
    except Exception as e:
        print(f"💥 读取 CSV 文件时发生错误: {e}")
    return accounts

def take_screenshot(driver, filename_prefix):
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.png"
    try:
        driver.save_screenshot(filename)
        print(f"  📸 已保存屏幕截图: {filename}")
    except Exception as e:
        print(f"  💥 无法保存屏幕截图: {e}")

def generate_fresh_totp_code(totp_secret):
    try:
        totp = pyotp.TOTP(totp_secret)
        remaining_time = totp.interval - (time.time() % totp.interval)
        if remaining_time < 3:
            print(f"  ⏳ 当前验证码仅剩 {remaining_time:.0f} 秒，等待新码...")
            time.sleep(remaining_time + 1)
        totp_code = totp.now()
        print(f"  🔑 已生成一个新鲜的 TOTP 验证码: {totp_code}")
        return totp_code
    except Exception as e:
        print(f"  💥 错误: 使用 pyotp 库生成验证码时出错: {e}")
        return None

# --- 主自动化函数 ---
def automate_registration(account_data, registration_url):
    driver = None
    try:
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument('--log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        
        prefs = {"credentials_enable_service": False, "profile.password_manager_enabled": False}
        options.add_experimental_option("prefs", prefs)

        # --- 终极修正: 使用进程创建标志来彻底隐藏ChromeDriver后台窗口和日志 ---
        # 这个标志告诉Windows在后台运行服务时不要创建控制台窗口
        creation_flags = subprocess.CREATE_NO_WINDOW
        
        # 将日志路径设为 'nul' (Windows的“黑洞”设备)，并传入创建标志
        service = Service(log_path='nul', creationflags=creation_flags)
        
        # 启动浏览器时同时传入 service 和 options 对象
        driver = webdriver.Chrome(service=service, options=options)
        # --- 修正结束 ---
        
        print(f"\n🚀 --- 尝试注册账号: {account_data['github_username']} ---")
        print(f"  ➡️  导航到注册页面: {registration_url}")
        driver.get(registration_url)
        
        wait = WebDriverWait(driver, 10)
        github_login_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'GitHub') or contains(., 'github')] | //a[contains(., 'GitHub') or contains(., 'github')]")))
        print("  ✅ 找到 GitHub 登录/注册按钮，点击中...")
        original_window_handle = driver.current_window_handle
        github_login_button.click()
        
        wait.until(EC.number_of_windows_to_be(2))
        for handle in driver.window_handles:
            if handle != original_window_handle:
                driver.switch_to.window(handle)
                break
        
        print(f"  👤 输入 GitHub 凭据: {account_data['github_username']}...")
        wait.until(EC.url_contains("github.com/login"))
        wait.until(EC.presence_of_element_located((By.ID, "login_field"))).send_keys(account_data['github_username'])
        driver.find_element(By.ID, "password").send_keys(account_data['github_password'])
        driver.find_element(By.NAME, "commit").click()

        try:
            tfa_wait = WebDriverWait(driver, 5)
            totp_input_field = tfa_wait.until(EC.presence_of_element_located((By.ID, "app_totp")))
            print("  📱 检测到 GitHub 2FA 页面。")
            totp_code = generate_fresh_totp_code(account_data['totp_secret'])
            if not totp_code: return False
            totp_input_field.send_keys(totp_code)
            print("  ✅ 2FA 输入完毕。")
        except TimeoutException:
            print(f"  ⏭️  未检测到 GitHub 2FA 页面，跳过。")

        try:
            auth_wait = WebDriverWait(driver, 8)
            authorize_button = auth_wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".js-oauth-authorize-btn")))
            print("  🛡️  检测到 GitHub 授权/确认页面。")
            try:
                parent_form = authorize_button.find_element(By.XPATH, "./ancestor::form")
                print("  ✅ 找到父表单，正在提交...")
                parent_form.submit()
            except NoSuchElementException:
                print("  ⚠️ 未找到父表单，回退到JavaScript强制点击。")
                driver.execute_script("arguments[0].click();", authorize_button)
            print("  ✅ 授权/确认步骤已执行。")
        except TimeoutException:
            print(f"  ⏭️  未检测到 GitHub 授权/确认页面，跳过。")

        print("  ⏳ GitHub流程完毕，等待并检查最终登录状态...")
        try:
            final_wait = WebDriverWait(driver, 15)
            final_wait.until(EC.url_contains("console/token"))
            print(f"🎉 账号 {account_data['github_username']} 注册/登录成功！")
            return True
        except TimeoutException:
            print(f"❌ 账号 {account_data['github_username']} 失败: 流程结束但未在15秒内跳转到 'console/token'。")
            take_screenshot(driver, "registration_timeout_error")
            return False

    except Exception as e:
        print(f"💥 账号 {account_data['github_username']} 自动化过程中发生未预期错误: {e}")
        if driver: take_screenshot(driver, "unexpected_error")
        return False
    finally:
        if driver:
            driver.quit()

# --- 主程序入口 ---
if __name__ == "__main__":
    
    print("🚀 --- 自动化注册脚本启动 --- 🚀")
    print("-" * 50)
    custom_url = input(f"请输入注册URL (直接回车将使用默认地址: {DEFAULT_REGISTRATION_URL}):\n> ")
    
    final_url = ""
    if not custom_url.strip():
        final_url = DEFAULT_REGISTRATION_URL
        print(f"➡️  未输入URL，将使用默认地址: {final_url}")
    else:
        final_url = custom_url.strip()
        print(f"➡️  将使用您输入的自定义地址: {final_url}")
    print("-" * 50)
    
    accounts = read_accounts_from_csv(CSV_FILE)
    if not accounts:
        print("🤷 没有可注册的账号。请检查 reg.csv 文件是否正确或为空。")
    else:
        print(f"📂 找到 {len(accounts)} 个账号待注册。")
        success_count = 0
        failure_count = 0
        for i, account in enumerate(accounts):
            if automate_registration(account, final_url):
                success_count += 1
            else:
                failure_count += 1
            
            if i < len(accounts) - 1:
                random_delay = random.uniform(1, 3)
                print(f"⏳ --- 等待 {random_delay:.2f} 秒后处理下一个账号 ---")
                time.sleep(random_delay)

        print("\n" + "="*50)
        print("🏁 --- 所有账号处理完成 --- 🏁")
        print(f"✔️  成功: {success_count}")
        print(f"❌ 失败: {failure_count}")