import csv
import time
import pyotp
import random
import subprocess
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service
import os

# --- 配置信息 ---
DEFAULT_REGISTRATION_URL = "https://anyrouter.top/register?aff=sUlE"
CSV_FILE = "reg.csv"
API_KEY_FILE = "key.md"

# --- 辅助函数 ---

def read_accounts_from_csv(file_path):
    accounts = []
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.reader(csvfile)
            for i, row in enumerate(reader):
                if row:
                    full_string = row[0].strip().strip('"')
                    parts = full_string.split('----')
                    if len(parts) >= 3:
                        accounts.append({
                            'github_username': parts[0].strip(),
                            'github_password': parts[1].strip(),
                            'totp_secret': parts[2].strip()
                        })
                    else:
                        print(f"⚠️  警告: CSV 文件第 {i+1} 行格式不正确，跳过: '{full_string}'.")
    except FileNotFoundError:
        print(f"💥 错误: CSV 文件未找到: '{file_path}'.")
    except Exception as e:
        print(f"💥 读取 CSV 文件时发生错误: {e}")
    return accounts

def take_screenshot(driver, filename_prefix):
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.png"
    try:
        driver.save_screenshot(filename)
        print(f"  📸 已保存屏幕截图: {filename}")
    except Exception as e:
        print(f"  💥 无法保存屏幕截图: {e}")

def generate_fresh_totp_code(totp_secret):
    try:
        totp = pyotp.TOTP(totp_secret)
        remaining_time = totp.interval - (time.time() % totp.interval)
        if remaining_time < 3:
            print(f"  ⏳ 当前验证码仅剩 {remaining_time:.0f} 秒，等待新码...")
            time.sleep(remaining_time + 1)
        totp_code = totp.now()
        print(f"  🔑 已生成一个新鲜的 TOTP 验证码: {totp_code}")
        return totp_code
    except Exception as e:
        print(f"  💥 错误: 使用 pyotp 库生成验证码时出错: {e}")
        return None

def create_and_save_api_key(driver, account_username):
    print(f"  ✨ 正在为账号 {account_username} 创建新的API密钥...")
    try:
        wait = WebDriverWait(driver, 10)
        
        add_button_xpath = "//button[.//span[text()='添加令牌']]"
        add_button = wait.until(EC.element_to_be_clickable((By.XPATH, add_button_xpath)))
        add_button.click()
        print("    ✅ 已点击“添加令牌”。")
        time.sleep(1)

        unlimited_button_xpath = "//button[.//span[text()='设为无限额度']]"
        unlimited_button = wait.until(EC.element_to_be_clickable((By.XPATH, unlimited_button_xpath)))
        unlimited_button.click()
        print("    ✅ 已设为无限额度。")
        time.sleep(1)

        submit_button_xpath = "//button[.//span[text()='提交']]"
        submit_button = wait.until(EC.element_to_be_clickable((By.XPATH, submit_button_xpath)))
        submit_button.click()
        print("    ✅ 已提交创建请求。")
        
        time.sleep(3)

        copy_button_xpath = "(//button[.//span[text()='复制']])[1]"
        copy_button = wait.until(EC.element_to_be_clickable((By.XPATH, copy_button_xpath)))
        copy_button.click()
        print("    ✅ 已点击“复制”按钮。")

        api_key = pyperclip.paste()
        if api_key and api_key.startswith("sk-"):
            print(f"    ✅ 成功从剪贴板获取API密钥: {api_key[:8]}... ")
            with open(API_KEY_FILE, 'a', encoding='utf-8') as f:
                f.write(f"{account_username}----{api_key}\n")
            print(f"    💾 已将账号和密钥保存到 {API_KEY_FILE}")
        else:
            print("    ❌ 从剪贴板获取API密钥失败或格式不正确。")

    except Exception as e:
        print(f"    💥 创建API密钥过程中发生错误: {e}")
        take_screenshot(driver, f"api_key_error_{account_username}")

def automate_registration(account_data, registration_url):
    driver = None
    try:
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument('--log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        prefs = {"credentials_enable_service": False, "profile.password_manager_enabled": False}
        options.add_experimental_option("prefs", prefs)
        
        creation_flags = 0
        if os.name == 'nt':
            creation_flags = subprocess.CREATE_NO_WINDOW
        
        service = Service(log_path='nul' if os.name == 'nt' else '/dev/null', creationflags=creation_flags)
        
        driver = webdriver.Chrome(service=service, options=options)
        
        print(f"🚀 --- 尝试注册账号: {account_data['github_username']} ---")
        print(f"  ➡️  导航到注册页面: {registration_url}")
        driver.get(registration_url)
        
        wait = WebDriverWait(driver, 10)
        github_login_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'GitHub') or contains(., 'github')] | //a[contains(., 'GitHub') or contains(., 'github')]")))
        print("  ✅ 找到 GitHub 登录/注册按钮，点击中...")
        original_window_handle = driver.current_window_handle
        github_login_button.click()
        
        wait.until(EC.number_of_windows_to_be(2))
        for handle in driver.window_handles:
            if handle != original_window_handle:
                driver.switch_to.window(handle)
                break
        
        print(f"  👤 输入 GitHub 凭据: {account_data['github_username']}...")
        wait.until(EC.url_contains("github.com/login"))
        wait.until(EC.presence_of_element_located((By.ID, "login_field"))).send_keys(account_data['github_username'])
        driver.find_element(By.ID, "password").send_keys(account_data['github_password'])
        driver.find_element(By.NAME, "commit").click()

        try:
            tfa_wait = WebDriverWait(driver, 5)
            totp_input_field = tfa_wait.until(EC.presence_of_element_located((By.ID, "app_totp")))
            print("  📱 检测到 GitHub 2FA 页面。")
            totp_code = generate_fresh_totp_code(account_data['totp_secret'])
            if not totp_code: return False, None
            totp_input_field.send_keys(totp_code)
            print("  ✅ 2FA 输入完毕。")
        except TimeoutException:
            print(f"  ⏭️  未检测到 GitHub 2FA 页面，跳过。")
        
        print("  ⏳ 2FA流程结束，动态等待 授权页面 或 最终跳转...")
        try:
            dynamic_wait = WebDriverWait(driver, 15)
            result = dynamic_wait.until(
                EC.any_of(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, ".js-oauth-authorize-btn")),
                    EC.url_contains("console/token")
                )
            )

            if isinstance(result, webdriver.remote.webelement.WebElement):
                print("  🛡️  检测到 GitHub 授权/确认页面。")
                authorize_button = result
                try:
                    parent_form = authorize_button.find_element(By.XPATH, "./ancestor::form")
                    print("  ✅ 找到父表单，正在提交...")
                    parent_form.submit()
                except NoSuchElementException:
                    print("  ⚠️ 未找到父表单，回退到JavaScript强制点击。")
                    driver.execute_script("arguments[0].click();", authorize_button)
                
                print("  ⏳ 授权已提交，等待最终跳转...")
                final_wait = WebDriverWait(driver, 15)
                final_wait.until(EC.url_contains("console/token"))
                print(f"🎉 账号 {account_data['github_username']} 注册/登录成功！")
                return True, driver
            else: 
                print(f"🎉 账号 {account_data['github_username']} 已直接跳转，注册/登录成功！")
                return True, driver

        except TimeoutException:
            print(f"❌ 账号 {account_data['github_username']} 失败: 在关键步骤后既未等到授权页面，也未跳转到成功URL。")
            take_screenshot(driver, "dynamic_wait_timeout_error")
            return False, driver

    except Exception as e:
        print(f"💥 账号 {account_data['github_username']} 自动化过程中发生未预期错误: {e}")
        if driver: take_screenshot(driver, "unexpected_error")
        return False, driver

# --- 主程序入口 ---
if __name__ == "__main__":
    
    print("🚀 --- 自动化注册脚本启动 --- 🚀")
    print("-" * 50)
    custom_url = input(f"请输入注册URL (直接回车将使用默认地址: {DEFAULT_REGISTRATION_URL}):\n> ")
    final_url = custom_url.strip() or DEFAULT_REGISTRATION_URL
    print(f"➡️  将使用地址: {final_url}")
    print("-" * 50)
    
    create_api_key_enabled = False
    choice = input("是否在注册成功后，自动新建并保存API密钥? (默认Y，输入N取消):\n> ").strip().upper()
    
    if choice == 'N':
        create_api_key_enabled = False
        print("❌ 已禁用: 注册成功后不会创建API密钥。")
    else:
        create_api_key_enabled = True
        print("✅ 已启用: 注册成功后将自动创建API密钥。")
    print("-" * 50)
    
    accounts = read_accounts_from_csv(CSV_FILE)
    if not accounts:
        print("🤷 没有可注册的账号。请检查 reg.csv 文件是否正确或为空。")
    else:
        # --- 新增功能: 初始化进度 ---
        total_accounts = len(accounts)
        print(f"📂 找到 {total_accounts} 个账号待注册。")
        success_count = 0
        failure_count = 0
        
        for i, account in enumerate(accounts):
            # --- 新增功能: 打印数字进度 ---
            print("\n" + "="*50)
            print(f"▶️  进度: ({i + 1}/{total_accounts})")
            
            success, driver_instance = automate_registration(account, final_url)
            
            if success and driver_instance:
                success_count += 1
                if create_api_key_enabled:
                    create_and_save_api_key(driver_instance, account['github_username'])
            else:
                failure_count += 1
            
            # --- 新增功能: 打印Emoji进度条 ---
            completed_count = success_count + failure_count
            pending_count = total_accounts - completed_count
            progress_bar = "✅" * success_count + "❌" * failure_count + "⏳" * pending_count
            print(f"📊 当前状态: {progress_bar} [{completed_count}/{total_accounts}]")

            if driver_instance:
                driver_instance.quit()

            if i < len(accounts) - 1:
                random_delay = random.uniform(1, 5)
                print(f"⏳ --- 等待 {random_delay:.2f} 秒后处理下一个账号 ---")
                time.sleep(random_delay)

        print("\n" + "="*50)
        print("🏁 --- 所有账号处理完成 --- 🏁")
        print(f"✔️  成功: {success_count}")
        print(f"❌ 失败: {failure_count}")