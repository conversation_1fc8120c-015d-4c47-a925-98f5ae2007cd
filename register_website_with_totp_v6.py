import csv
import time
import pyotp
import random
import subprocess
import os
from typing import List, Dict, <PERSON><PERSON>, Optional

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchWindowException, StaleElementReferenceException
from selenium.webdriver.common.action_chains import ActionChains

# --- 配置信息 ---
DEFAULT_LOGIN_URL = "https://anyrouter.top"
CSV_FILE = "reg.csv"

# --- 元素定位器 (Locators) ---

class WebsiteLocators:
    """存放目标网站的元素定位器"""
    TOP_RIGHT_LOGIN_BUTTON = (By.XPATH, "//a[@href='/login' and .//button]")
    ANNOUNCEMENT_CLOSE_BUTTON = (By.XPATH, "//div[@role='dialog']//button[@aria-label='close']")

class GitHubLocators:
    LOGIN_BUTTON = (By.XPATH, "//button[contains(., 'GitHub') or contains(., 'github')] | //a[contains(., 'GitHub') or contains(., 'github')]")
    USERNAME_INPUT = (By.ID, "login_field")
    PASSWORD_INPUT = (By.ID, "password")
    SIGN_IN_BUTTON = (By.NAME, "commit")
    TOTP_INPUT = (By.ID, "app_totp")
    POST_TOTP_VERIFY_BUTTON = (By.XPATH, "//button[contains(., 'Verify')]")
    DONE_BUTTON = (By.XPATH, "//a[contains(@class, 'Button--primary')]//span[contains(., '完毕') or contains(., 'Done')]")
    AUTHORIZE_BUTTON = (By.CSS_SELECTOR, ".js-oauth-authorize-btn, #js-oauth-authorize-btn")


# --- 辅助函数 (无修改) ---

def handle_announcement_modal(driver: WebDriver):
    print("  📢 正在动态检测系统公告...")
    try:
        wait = WebDriverWait(driver, 5, ignored_exceptions=[StaleElementReferenceException])
        close_button = wait.until(EC.element_to_be_clickable(WebsiteLocators.ANNOUNCEMENT_CLOSE_BUTTON))
        print("  ✅ 检测到公告面板，正在强制关闭...")
        driver.execute_script("arguments[0].click();", close_button)
        print("  ✅ 公告已关闭。")
        time.sleep(1)
    except TimeoutException:
        print("  ✅ 未检测到可操作的公告面板，继续。")

def read_accounts_from_csv(file_path: str) -> List[Dict[str, str]]:
    accounts = []
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.reader(csvfile)
            for i, row in enumerate(reader):
                if not row: continue
                full_string = row[0].strip().strip('"')
                parts = full_string.split('----')
                if len(parts) >= 3:
                    accounts.append({'github_username': parts[0].strip(), 'github_password': parts[1].strip(), 'totp_secret': parts[2].strip()})
                else:
                    print(f"⚠️  警告: CSV 文件第 {i+1} 行格式不正确，跳过: '{full_string}'.")
    except FileNotFoundError:
        print(f"💥 错误: CSV 文件未找到: '{file_path}'.")
    except Exception as e:
        print(f"💥 读取 CSV 文件时发生错误: {e}")
    return accounts

def take_screenshot(driver: WebDriver, filename_prefix: str):
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.png"
    try:
        driver.save_screenshot(filename)
        print(f"  📸 已保存屏幕截图: {filename}")
    except Exception as e:
        print(f"  💥 无法保存屏幕截图: {e}")

def generate_fresh_totp_code(totp_secret: str) -> Optional[str]:
    try:
        totp = pyotp.TOTP(totp_secret)
        remaining_time = totp.interval - (time.time() % totp.interval)
        if remaining_time < 5:
            print(f"  ⏳ 当前验证码仅剩 {remaining_time:.0f} 秒，等待新码...")
            time.sleep(remaining_time + 2)
        totp_code = totp.now()
        print(f"  🔑 已生成一个新鲜的 TOTP 验证码: {totp_code}")
        return totp_code
    except Exception as e:
        print(f"  💥 错误: 使用 pyotp 库生成验证码时出错: {e}")
        return None

def create_chrome_driver() -> Optional[WebDriver]:
    try:
        options = webdriver.ChromeOptions()
        options.add_argument("--start-maximized")
        options.add_argument('--log-level=3')
        options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        prefs = {"credentials_enable_service": False, "profile.password_manager_enabled": False}
        options.add_experimental_option("prefs", prefs)
        
        creation_flags = 0
        log_path = 'nul' if os.name == 'nt' else '/dev/null'
        if os.name == 'nt':
            creation_flags = subprocess.CREATE_NO_WINDOW
        
        service = Service(log_path=log_path, creationflags=creation_flags if os.name == 'nt' else 0)
        
        return webdriver.Chrome(service=service, options=options)
    except Exception as e:
        print(f"💥 创建 WebDriver 时发生严重错误: {e}")
        return None

# --- 核心逻辑函数 (已重构) ---
def automate_login(account_data: Dict[str, str], login_url: str) -> Tuple[bool, Optional[WebDriver]]:
    """执行单个账户的完整登录流程，内置更强大的动态检测机制。"""
    driver = create_chrome_driver()
    if not driver:
        return False, None

    try:
        print(f"🚀 --- 尝试登录账号: {account_data['github_username']} ---")
        print(f"  ➡️  导航到初始页面: {login_url}")
        driver.get(login_url)
        
        handle_announcement_modal(driver)

        wait = WebDriverWait(driver, 10)
        
        print("  1. 正在寻找并点击'已有账号，登录'按钮...")
        try:
            pre_login_button = wait.until(EC.element_to_be_clickable(WebsiteLocators.TOP_RIGHT_LOGIN_BUTTON))
            driver.execute_script("arguments[0].click();", pre_login_button)
            print("  ✅ 已点击右上角'登录'按钮。")
        except TimeoutException:
            print("  ❌ 错误: 未能找到或点击初始的'登录'按钮。")
            take_screenshot(driver, "pre_login_button_not_found")
            return False, driver
        
        print("  2. 正在点击'GitHub'登录按钮以弹出验证窗口...")
        original_window_handle = driver.current_window_handle
        github_login_button = wait.until(EC.element_to_be_clickable(GitHubLocators.LOGIN_BUTTON))
        driver.execute_script("arguments[0].click();", github_login_button)
        print("  ✅ GitHub 按钮已点击，等待弹窗...")
        
        # --- 处理GitHub弹窗 ---
        wait.until(EC.number_of_windows_to_be(2))
        for handle in driver.window_handles:
            if handle != original_window_handle:
                driver.switch_to.window(handle)
                print("  ✅ 已切换到 GitHub 验证弹窗。")
                break
        
        print(f"  👤 输入 GitHub 凭据: {account_data['github_username']}...")
        wait.until(EC.url_contains("github.com/login"))
        wait.until(EC.presence_of_element_located(GitHubLocators.USERNAME_INPUT)).send_keys(account_data['github_username'])
        driver.find_element(*GitHubLocators.PASSWORD_INPUT).send_keys(account_data['github_password'])
        driver.find_element(*GitHubLocators.SIGN_IN_BUTTON).click()

        # --- 新的动态2FA及后续流程处理 ---
        print("  3. 开始动态处理2FA及后续流程...")
        action_needed = 'switch_back'
        try:
            totp_input_field = WebDriverWait(driver, 10).until(EC.presence_of_element_located(GitHubLocators.TOTP_INPUT))
            print("  📱 检测到 GitHub 2FA 页面。")
            totp_code = generate_fresh_totp_code(account_data['totp_secret'])
            if not totp_code: return False, driver
            totp_input_field.send_keys(totp_code)
            print("  ✅ 2FA 验证码输入完毕。")

            try:
                verify_wait = WebDriverWait(driver, 2, ignored_exceptions=[StaleElementReferenceException])
                verify_button = verify_wait.until(EC.element_to_be_clickable(GitHubLocators.POST_TOTP_VERIFY_BUTTON))
                print("  🔍 探测结果: 需要二次验证。点击 'Verify'...")
                verify_button.click()
                
                print("  ⏳ 检测到二次验证，强制等待新的TOTP时间窗口...")
                remaining_time = 30 - (time.time() % 30)
                wait_duration = remaining_time + 2
                print(f"  ⏳ 等待 {int(wait_duration)} 秒以获取全新验证码...")
                time.sleep(wait_duration)

                second_totp_input = WebDriverWait(driver, 5).until(EC.presence_of_element_located(GitHubLocators.TOTP_INPUT))
                totp_code_2 = generate_fresh_totp_code(account_data['totp_secret'])
                if not totp_code_2: return False, driver
                second_totp_input.send_keys(totp_code_2)
                print("  ✅ 第二次 2FA 验证码输入完毕。")

            except TimeoutException:
                print("  🔍 探测结果: 无需二次验证。")

            try:
                final_button_wait = WebDriverWait(driver, 2, ignored_exceptions=[StaleElementReferenceException])
                final_button = final_button_wait.until(EC.any_of(
                    EC.element_to_be_clickable(GitHubLocators.DONE_BUTTON),
                    EC.element_to_be_clickable(GitHubLocators.AUTHORIZE_BUTTON)
                ))
                button_text = final_button.text.lower()
                if 'done' in button_text or '完毕' in button_text:
                    print("  🔍 探测结果: 出现'完毕'按钮。")
                    final_button.click()
                    action_needed = 're-click'
                elif 'authorize' in button_text:
                    print("  🔍 探测结果: 出现'Authorize'按钮。")
                    final_button.click()
            except TimeoutException:
                print("  🔍 探测结果: 未发现'Done'或'Authorize'按钮。")
            
            print("  🔍 优先检测: 检查弹窗本身是否已跳转到成功URL...")
            try:
                WebDriverWait(driver, 2).until(EC.url_contains("console/token"))
                print(f"🎉 账号 {account_data['github_username']} 登录成功！(在弹窗中直接跳转)")
                return True, driver
            except TimeoutException:
                print("  ✅ 弹窗未直接跳转，将按计划返回主页面进行最终验证。")
        
        except StaleElementReferenceException:
             print("  💥 捕获到顶层StaleElementReferenceException，流程可能已改变，继续最终验证。")
        except TimeoutException:
            print("  ✅ 未检测到2FA页面，可能已直接授权或出现其他情况。")

        # --- V9.0 终极重构：多路径最终验证 ---
        print("  4. 最终验证: 等待或确认主页面登录成功...")
        try:
            if len(driver.window_handles) > 1:
                for handle in driver.window_handles:
                    if handle != original_window_handle:
                        try:
                            driver.switch_to.window(handle)
                            driver.close()
                        except NoSuchWindowException:
                            pass
            driver.switch_to.window(original_window_handle)
            print("  ✅ 已确保控制权返回主页面。")
            
            if action_needed == 're-click':
                print("  📢 (二次检查) 返回主页后，再次动态检测系统公告...")
                handle_announcement_modal(driver)

                print("  🖱️ 在主页面上再次点击'GitHub'登录按钮...")
                github_button_again = wait.until(EC.element_to_be_clickable(GitHubLocators.LOGIN_BUTTON))
                driver.execute_script("arguments[0].click();", github_button_again)
                
                print("  ⏱️ 增加2秒战略性停顿，等待新标签页或页面响应...")
                time.sleep(2)
            
            # --- 检查是否打开了新标签页 ---
            if len(driver.window_handles) > 1:
                print("  🔍 探测结果: 检测到新标签页打开，切换以进行验证...")
                new_tab_handle = [handle for handle in driver.window_handles if handle != original_window_handle][0]
                driver.switch_to.window(new_tab_handle)
                WebDriverWait(driver, 15).until(EC.url_contains("console/token"))
                print(f"🎉 账号 {account_data['github_username']} 登录成功！(在新标签页中)")
                return True, driver
            else:
            # --- 如果没有新标签页，则在当前窗口等待 ---
                print("  🔍 探测结果: 未检测到新标签页，在当前窗口等待跳转...")
                if "console/token" in driver.current_url:
                    print("  ✅ 已立即检测到成功URL。")
                else:
                    print("  ⏳ 页面尚未跳转，开始等待...")
                    WebDriverWait(driver, 20).until(EC.url_contains("console/token"))
                    print("  ✅ 等待后检测到成功URL。")
                
                print(f"🎉 账号 {account_data['github_username']} 登录成功！")
                return True, driver
        except TimeoutException:
            print(f"❌ 最终验证失败: 等待后，在任何预期的位置都未找到 'console/token' URL。")
            take_screenshot(driver, "final_redirect_timeout")
            return False, driver

    except Exception as e:
        print(f"💥 账号 {account_data['github_username']} 自动化过程中发生未预期错误: {e}")
        take_screenshot(driver, "unexpected_error")
        return False, driver


def main():
    """脚本主执行函数"""
    print("🚀 --- 自动化登录脚本启动 (V9.0 - 终极多路径版) --- 🚀")
    print("-" * 50)
    custom_url = input(f"请输入登录URL (直接回车将使用默认地址: {DEFAULT_LOGIN_URL}):\n> ")
    final_url = custom_url.strip() or DEFAULT_LOGIN_URL
    print(f"➡️  将使用地址: {final_url}")
    print("-" * 50)
    
    accounts = read_accounts_from_csv(CSV_FILE)
    if not accounts:
        print("🤷 没有可登录的账号。请检查 reg.csv 文件是否正确或为空。")
        return

    total_accounts = len(accounts)
    print(f"📂 找到 {total_accounts} 个账号待登录。")
    success_count = 0
    failure_count = 0
    
    for i, account in enumerate(accounts):
        print("\n" + "="*50)
        print(f"▶️  进度: ({i + 1}/{total_accounts})")
        
        driver_instance = None
        login_success = False
        try:
            login_success, driver_instance = automate_login(account, final_url)
            
            if login_success:
                success_count += 1
            else:
                failure_count += 1
        except Exception as main_e:
            print(f"💥 Main loop 捕获到严重异常: {main_e}")
            failure_count += 1
        finally:
            if driver_instance:
                if login_success:
                    print("  ✅ 登录成功，停留3秒后关闭浏览器...")
                    time.sleep(3)
                driver_instance.quit()

        completed_count = success_count + failure_count
        pending_count = total_accounts - completed_count
        progress_bar = "✅" * success_count + "❌" * failure_count + "⏳" * pending_count
        print(f"📊 当前状态: {progress_bar} [{completed_count}/{total_accounts}]")

        if i < len(accounts) - 1:
            random_delay = random.uniform(2, 6)
            print(f"⏳ --- 等待 {random_delay:.2f} 秒后处理下一个账号 ---")
            time.sleep(random_delay)

    print("\n" + "="*50)
    print("🏁 --- 所有账号处理完成 --- 🏁")
    print(f"✔️  成功: {success_count}")
    print(f"❌ 失败: {failure_count}")


if __name__ == "__main__":
    main()